<template>
  <div class="audit-example">
    <h2>审核弹窗使用示例</h2>

    <!-- 模拟数据表格 -->
    <a-table :columns="columns" :data-source="mockData" :pagination="false">
      <template #action="{ record }">
        <a-button type="primary" size="small" @click="handleAudit(record)"> 审核 </a-button>
        <a-button type="link" size="small" @click="handleViewItems(record)"> 查看标的 </a-button>
      </template>
    </a-table>

    <!-- 审核弹窗 -->
    <CommonAuditModal v-model:open="auditModalVisible" :record="currentAuditRecord" @close="handleCloseAuditModal" @success="handleAuditComplete" />

    <!-- 标的列表弹窗 -->
    <CommonAuctionItemsModal v-model:open="itemsModalVisible" :record-id="currentRecordId" @close="itemsModalVisible = false" />
  </div>
</template>

<script setup lang="ts">
  import { ref } from 'vue';
  import { message } from 'ant-design-vue';
  import { CommonAuditModal, CommonAuctionItemsModal, type AuditRecord } from '/@/components/Audit';

  // 表格列定义
  const columns = [
    {
      title: '委托单号',
      dataIndex: 'id',
      key: 'id',
    },
    {
      title: '项目名称',
      dataIndex: 'projectName',
      key: 'projectName',
    },
    {
      title: '委托类型',
      dataIndex: 'entrustType',
      key: 'entrustType',
      customRender: ({ text }: any) => {
        const typeMap: Record<number, string> = {
          1: '增值委托',
          2: '自主委托',
          3: '供求信息',
        };
        return typeMap[text] || '未知';
      },
    },
    {
      title: '服务类型',
      dataIndex: 'serviceType',
      key: 'serviceType',
      customRender: ({ text }: any) => {
        const typeMap: Record<number, string> = {
          1: '竞价委托',
          2: '资产处置',
          3: '采购信息',
          4: '供应',
          5: '求购',
        };
        return typeMap[text] || '未知';
      },
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      customRender: ({ text }: any) => {
        const statusMap: Record<number, string> = {
          2: '待审核',
          3: '已通过',
          4: '已拒绝',
        };
        return statusMap[text] || '未知';
      },
    },
    {
      title: '操作',
      key: 'action',
      slots: { customRender: 'action' },
    },
  ];

  // 模拟数据
  const mockData: AuditRecord[] = [
    {
      id: 'ENT202401001',
      entrustType: 1,
      serviceType: 1,
      status: 2,
      projectName: '办公设备处置项目',
      relationUser: '张三',
      relationPhone: '13800138000',
      applicantUser: '李四',
      auditUser: '',
      submitTime: '2024-01-15 10:30:00',
      auditTime: '',
    },
    {
      id: 'ENT202401002',
      entrustType: 2,
      serviceType: 1,
      status: 2,
      projectName: '车辆拍卖委托',
      relationUser: '王五',
      relationPhone: '13800138001',
      applicantUser: '赵六',
      auditUser: '',
      submitTime: '2024-01-16 14:20:00',
      auditTime: '',
    },
    {
      id: 'ENT202401003',
      entrustType: 1,
      serviceType: 2,
      status: 3,
      projectName: '资产处置委托',
      relationUser: '孙七',
      relationPhone: '13800138002',
      applicantUser: '周八',
      auditUser: '管理员',
      submitTime: '2024-01-14 09:15:00',
      auditTime: '2024-01-14 16:30:00',
    },
    {
      id: 'ENT202401004',
      entrustType: 3,
      serviceType: 4,
      status: 2,
      projectName: '钢材供应信息',
      relationUser: '陈九',
      relationPhone: '13800138003',
      applicantUser: '吴十',
      auditUser: '',
      submitTime: '2024-01-17 11:45:00',
      auditTime: '',
    },
    {
      id: 'ENT202401005',
      entrustType: 3,
      serviceType: 5,
      status: 2,
      projectName: '设备求购信息',
      relationUser: '刘十一',
      relationPhone: '13800138004',
      applicantUser: '郑十二',
      auditUser: '',
      submitTime: '2024-01-18 08:30:00',
      auditTime: '',
    },
  ];

  // 审核弹窗状态
  const auditModalVisible = ref(false);
  const currentAuditRecord = ref<AuditRecord | null>(null);

  // 标的列表弹窗状态
  const itemsModalVisible = ref(false);
  const currentRecordId = ref('');

  // 事件处理函数
  function handleAudit(record: AuditRecord) {
    currentAuditRecord.value = record;
    auditModalVisible.value = true;
    console.log('审核记录:', record);
  }

  function handleViewItems(record: AuditRecord) {
    currentRecordId.value = record.id;
    itemsModalVisible.value = true;
  }

  // 关闭审核弹窗
  function handleCloseAuditModal() {
    auditModalVisible.value = false;
    currentAuditRecord.value = null;
  }

  // 审核完成后的回调
  function handleAuditComplete() {
    handleCloseAuditModal();
    message.success('审核操作完成');
    // 这里可以刷新列表或执行其他操作
  }
</script>

<style lang="less" scoped>
  .audit-example {
    padding: 20px;

    h2 {
      margin-bottom: 20px;
      color: #333;
    }
  }
</style>
