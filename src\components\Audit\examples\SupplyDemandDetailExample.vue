<template>
  <div class="supply-demand-detail-example">
    <h2>供求详情查看示例</h2>
    
    <!-- 模拟数据表格 -->
    <a-table :columns="columns" :data-source="mockData" :pagination="false">
      <template #entrustType="{ text }">
        <a-tag :color="getEntrustTypeColor(text)">
          {{ getEntrustTypeText(text) }}
        </a-tag>
      </template>
      
      <template #serviceType="{ text }">
        <a-tag :color="getServiceTypeColor(text)">
          {{ getServiceTypeText(text) }}
        </a-tag>
      </template>
      
      <template #status="{ text }">
        <a-tag :color="getStatusColor(text)">
          {{ getStatusText(text) }}
        </a-tag>
      </template>
      
      <template #action="{ record }">
        <a-button type="primary" size="small" @click="handleViewDetail(record)">
          查看详情
        </a-button>
      </template>
    </a-table>

    <!-- 详情查看弹窗 -->
    <DetailViewModal
      v-model:open="detailVisible"
      :record="currentRecord"
      :entrust-type="currentRecord?.entrustType || 3"
      :service-type="currentRecord?.serviceType || 4"
      @close="handleDetailClose"
    />
  </div>
</template>

<script setup lang="ts">
  import { ref } from 'vue';
  import { message } from 'ant-design-vue';
  import { DetailViewModal, type AuditRecord } from '/@/components/Audit';

  // 表格列定义
  const columns = [
    {
      title: '委托单号',
      dataIndex: 'id',
      key: 'id',
    },
    {
      title: '项目名称',
      dataIndex: 'projectName',
      key: 'projectName',
    },
    {
      title: '委托类型',
      dataIndex: 'entrustType',
      key: 'entrustType',
      slots: { customRender: 'entrustType' },
    },
    {
      title: '服务类型',
      dataIndex: 'serviceType',
      key: 'serviceType',
      slots: { customRender: 'serviceType' },
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      slots: { customRender: 'status' },
    },
    {
      title: '联系人',
      dataIndex: 'relationUser',
      key: 'relationUser',
    },
    {
      title: '操作',
      key: 'action',
      slots: { customRender: 'action' },
    },
  ];

  // 供求类型的模拟数据
  const mockData: AuditRecord[] = [
    {
      id: 'SUP202401001',
      entrustType: 3,
      serviceType: 4,
      status: 3,
      projectName: '钢材供应信息',
      relationUser: '陈九',
      relationPhone: '13800138003',
      applicantUser: '吴十',
      auditUser: '管理员',
      submitTime: '2024-01-17 11:45:00',
      auditTime: '2024-01-17 16:30:00',
    },
    {
      id: 'DEM202401001',
      entrustType: 3,
      serviceType: 5,
      status: 3,
      projectName: '设备求购信息',
      relationUser: '刘十一',
      relationPhone: '13800138004',
      applicantUser: '郑十二',
      auditUser: '管理员',
      submitTime: '2024-01-18 08:30:00',
      auditTime: '2024-01-18 14:20:00',
    },
    {
      id: 'SUP202401002',
      entrustType: 3,
      serviceType: 4,
      status: 4,
      projectName: '建材供应信息',
      relationUser: '王五',
      relationPhone: '13800138005',
      applicantUser: '李六',
      auditUser: '管理员',
      submitTime: '2024-01-19 10:15:00',
      auditTime: '2024-01-19 15:45:00',
    },
  ];

  // 详情查看弹窗状态
  const detailVisible = ref(false);
  const currentRecord = ref<AuditRecord | null>(null);

  // 事件处理函数
  function handleViewDetail(record: AuditRecord) {
    currentRecord.value = record;
    detailVisible.value = true;
    console.log('查看供求详情:', record);
    message.info(`正在查看 ${record.projectName} 的详情`);
  }

  // 关闭详情弹窗
  function handleDetailClose() {
    detailVisible.value = false;
    currentRecord.value = null;
  }

  // 委托类型处理函数
  function getEntrustTypeText(type: number) {
    const typeMap: Record<number, string> = {
      1: '增值',
      2: '自主',
      3: '供求',
    };
    return typeMap[type] || '未知';
  }

  function getEntrustTypeColor(type: number) {
    const colorMap: Record<number, string> = {
      1: 'blue',
      2: 'green',
      3: 'orange',
    };
    return colorMap[type] || 'default';
  }

  // 服务类型处理函数
  function getServiceTypeText(type: number) {
    const typeMap: Record<number, string> = {
      1: '竞价委托',
      2: '资产处置',
      3: '采购信息',
      4: '供应',
      5: '求购',
    };
    return typeMap[type] || '未知';
  }

  function getServiceTypeColor(type: number) {
    const colorMap: Record<number, string> = {
      1: 'purple',
      2: 'cyan',
      3: 'geekblue',
      4: 'lime',
      5: 'magenta',
    };
    return colorMap[type] || 'default';
  }

  // 审核状态处理函数
  function getStatusText(status: number) {
    const statusMap: Record<number, string> = {
      1: '草稿',
      2: '待审核',
      3: '已通过',
      4: '已拒绝',
    };
    return statusMap[status] || '未知';
  }

  function getStatusColor(status: number) {
    const colorMap: Record<number, string> = {
      2: 'processing',
      3: 'success',
      4: 'error',
    };
    return colorMap[status] || 'default';
  }
</script>

<style lang="less" scoped>
  .supply-demand-detail-example {
    padding: 24px;
    
    h2 {
      margin-bottom: 24px;
      color: #262626;
    }
  }
</style>
